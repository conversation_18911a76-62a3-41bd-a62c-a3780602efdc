import asyncio
from pathlib import Path

from prefect import get_client
from prefect.client.schemas.objects import DeploymentResponse, WorkPool, WorkPoolCreate
from prefect.deployments import Deployment
from prefect.deployments.deployments import load_flow_from_entrypoint
from shared.python.utils.get_aws_config import get_config

from shared.python.utils.get_aws_config import get_aws_config
from shared.python.utils.process_resources import get_process_resources

# Get configuration
bootstrap_config_path = "docker/prefect/prefect_bootstrap_config.yaml")

bootstrap_config = get_config(bootstrap_config_path)

aws_config = get_aws_config()
process_name = bootstrap_config.get("process_name")

# Get resources for the process
relevant_process_resources = get_process_resources(aws_config, process_name)

test = get_process_resources("prefect-bootstrap")

exit()

# === CONFIGURATION ===
WORK_POOL_NAME = "ecs-shared-pool"
DEPLOYMENT_NAME = "task-a-deployment"
FLOW_PATH = "flows/my_flow.py:my_flow"  # Must exist locally
DOCKER_IMAGE = "your-ecr-repo/task-a:latest"
TASK_DEF_ARN = "arn:aws:ecs:region:account-id:task-definition/task-a:3"
ECS_REGION = "us-west-2"
ECS_CLUSTER = "your-ecs-cluster"
ECS_SUBNETS = ["subnet-123abc"]
ECS_SGS = ["sg-123abc"]
ECS_EXEC_ROLE = "arn:aws:iam::account-id:role/ecsTaskExecutionRole"

# === JOB TEMPLATE OVERRIDE ===
JOB_TEMPLATE = {
    "variables": {
        "type": "object",
        "properties": {
            "region": {"type": "string", "default": ECS_REGION},
            "cluster": {"type": "string", "default": ECS_CLUSTER},
            "taskDefinitionArn": {"type": "string", "default": TASK_DEF_ARN},
            "subnets": {
                "type": "array",
                "items": {"type": "string"},
                "default": ECS_SUBNETS,
            },
            "securityGroups": {
                "type": "array",
                "items": {"type": "string"},
                "default": ECS_SGS,
            },
            "executionRoleArn": {"type": "string", "default": ECS_EXEC_ROLE},
        },
        "required": ["cluster", "taskDefinitionArn", "subnets", "region"],
    }
}


async def main():
    async with get_client() as client:
        # === Step 1: Ensure Work Pool ===
        try:
            existing_pool: WorkPool = await client.read_work_pool(WORK_POOL_NAME)
            print(f"✅ Work pool '{WORK_POOL_NAME}' already exists.")
        except Exception:
            print(f"🛠 Creating work pool '{WORK_POOL_NAME}'...")
            await client.create_work_pool(
                name=WORK_POOL_NAME, type="ecs", base_job_template=JOB_TEMPLATE
            )

        # === Step 2: Ensure Deployment ===
        existing_deployments = await client.read_deployments(name=DEPLOYMENT_NAME)
        if existing_deployments:
            print(f"✅ Deployment '{DEPLOYMENT_NAME}' already exists.")
        else:
            print(f"🛠 Creating deployment '{DEPLOYMENT_NAME}'...")
            flow = load_flow_from_entrypoint(FLOW_PATH)

            deployment = Deployment.build_from_flow(
                flow=flow,
                name=DEPLOYMENT_NAME,
                work_pool_name=WORK_POOL_NAME,
                image=DOCKER_IMAGE,
                path=".",  # project path inside container
                entrypoint=FLOW_PATH,
            )
            deployment.apply()
            print(f"✅ Deployment '{DEPLOYMENT_NAME}' registered.")


if __name__ == "__main__":
    asyncio.run(main())
