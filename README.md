Steps for new env:

1. Create a new aws Accounts
2. Create a developer IAM user and access keys, MFA, bastion-key-pair
3. Add to credentials
4. Enable OIDC authentication https://www.youtube.com/watch?v=aOoRaVuh8Lc
5. Create an ECR repository Not sure if this one is relevant still?


TODO:
get auto ssh working from wsl to devcontainer

upgrade node

check subnet route tables to make sure private

Put specific permissions in config and let cdk apply dynamically

Problems


Metrics to track

Advanced Efficiency Metrics

True Shooting Percentage (TS%): Measures shooting efficiency accounting for 2-pointers, 3-pointers, and free throws
Effective Field Goal Percentage (eFG%): Similar to TS% but without free throws
Player Efficiency Rating (PER): Comprehensive measure of per-minute production

Impact Metrics

Plus/Minus: Net scoring while a player is on the court
Adjusted Plus/Minus: Controls for teammates and opponents
RAPM (Regularized Adjusted Plus/Minus): More stable version using regression techniques
LEBRON (Luck-adjusted player Estimate using a Box prior RegularizatiON): Combines box score stats with on/off data

Possession-Based Stats

Usage Rate: Percentage of team plays used by a player
Assist Percentage: Percentage of teammate field goals a player assisted
Defensive Rating: Points allowed per 100 possessions
Offensive Rating: Points scored per 100 possessions

Spacing and Positioning

Gravity Score: Measures how much a player draws defensive attention
Spacing Impact: How a player's positioning affects team offensive efficiency
Contested Shot Rate: Percentage of shots defended effectively

Tracking Data Insights

Distance Traveled: Total movement during games
Average Speed: How fast players move in different scenarios
Paint Touches: How often a player touches the ball in the paint
Secondary Assists: Passes leading to an assist

Load Management Indicators

VORP (Value Over Replacement Player): Estimates value compared to a replacement-level player
Load Management Factor: Measures workload impact on performance