from shared.python.utils.get_aws_config import get_aws_config
from src.tasks.extraction.utils.api_client import initialize_api
from src.tasks.extraction.utils.endpoint_processor import (
    filter_endpoints,
    get_requested_endpoints,
    process_endpoints,
    save_endpoint_results,
)
from src.tasks.extraction.utils.get_extraction_config import (
    get_api_config,
    get_process_resources,
    substitute_placeholders,
)
from src.tasks.extraction.utils.resource_manager import get_api_key, get_s3_bucket_name


def main():
    """
    Main function to orchestrate the data extraction process.

    Returns:
        int: Exit code (0 for success)
    """
    # Get configuration
    api_config = get_api_config(
        "src/tasks/extraction/configs/balldontlie_api_extraction.yaml"
    )
    aws_config = get_aws_config()
    process_name = api_config.get("process_name")

    # Get resources for the process
    relevant_process_resources = get_process_resources(aws_config, process_name)

    # Get API key from secrets manager
    api_key = get_api_key(relevant_process_resources)
    api_config = substitute_placeholders(api_config, api_key=api_key)

    # Get S3 bucket name for storing the extracted data
    source_bucket_name = get_s3_bucket_name(relevant_process_resources)

    # Initialize the API client with the full configuration
    api_instance = initialize_api(api_config)

    # Determine which endpoints to process
    requested_endpoints = get_requested_endpoints()
    endpoints = api_config.get("endpoints", {})
    filtered_endpoints, log_message = filter_endpoints(endpoints, requested_endpoints)
    print(log_message)

    # Process the endpoints
    results = process_endpoints(api_instance, filtered_endpoints)

    # Save the results to S3
    save_endpoint_results(results, source_bucket_name)

    return 0


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
