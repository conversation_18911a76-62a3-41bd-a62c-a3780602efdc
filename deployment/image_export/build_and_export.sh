#!/bin/bash
# /betting_exchange/deployment/image_export/build_and_export.sh

# Source shared functions
SCRIPT_DIR=$(dirname "$(realpath "$0")")
ROOT_PATH=$(realpath "$SCRIPT_DIR/../..")
source "$ROOT_PATH/shared/bash/functions.sh" || { echo "ERROR: Failed to source shared functions"; exit 1; }

echo "Building and exporting Docker images..."

# Determine the base repository path (up two directories, then into "docker")
BASE_REPO_PATH=$(realpath "$SCRIPT_DIR/../../docker") || handle_error "Failed to determine base repository path"

# Determine if a specific repo path was provided as an argument
if [ -z "$1" ]; then
    echo "No argument provided. Processing all subfolders in '$BASE_REPO_PATH'."
    TARGET_FOLDERS=("$BASE_REPO_PATH"/*)  # Get all subdirectories inside 'docker/'
else
    TARGET_FOLDERS=("$1")
    echo "Processing single repository folder: $1"
fi

# Ensure the base directory exists
if [ ! -d "$BASE_REPO_PATH" ]; then
    handle_error "Directory '$BASE_REPO_PATH' does not exist"
fi

# Get AWS account ID and ECR URL
AWS_ACCOUNT_ID=$(get_aws_account_id)
ECR_URL=$(get_ecr_url "$AWS_ACCOUNT_ID")
echo "Using ECR URL: $ECR_URL"

# Authenticate Docker to AWS ECR
login_to_ecr "$ECR_URL"

# Iterate over the specified target folders
for REPO_PATH in "${TARGET_FOLDERS[@]}"; do
    if [ ! -d "$REPO_PATH" ]; then
        echo "Skipping '$REPO_PATH' - Not a valid directory."
        continue
    fi

    REPO_NAME=$(basename "$REPO_PATH")

    # Check if the repository exists in ECR
    EXISTING_REPO=$(aws ecr describe-repositories --repository-names "$REPO_NAME" --region "$AWS_REGION" 2>/dev/null || true)

    if [ -z "$EXISTING_REPO" ]; then
        echo "ECR repository '$REPO_NAME' does not exist. Creating..."
        aws ecr create-repository --repository-name "$REPO_NAME" --region "$AWS_REGION" > /dev/null 2>&1 || handle_error "Failed to create ECR repository '$REPO_NAME'"
        
        # Apply lifecycle policy to the newly created repository
        POLICY_FILE="$SCRIPT_DIR/ecr_lifecycle_policy.json"
        if [ -f "$POLICY_FILE" ]; then
            echo "Applying lifecycle policy to keep only 5 images for repository '$REPO_NAME'..."
            aws ecr put-lifecycle-policy --repository-name "$REPO_NAME" --lifecycle-policy-text file://"$POLICY_FILE" --region "$AWS_REGION" > /dev/null 2>&1 || handle_error "Failed to apply lifecycle policy to repository '$REPO_NAME'"
        else
            echo "Warning: Lifecycle policy file not found at $POLICY_FILE"
        fi
    fi

    # Iterate over subfolders (each is a separate image in the repo)
    for SUBFOLDER in "$REPO_PATH"/*; do
        if [ -d "$SUBFOLDER" ]; then
            IMAGE_NAME=$(basename "$SUBFOLDER")  # Subfolder name as image name
            DOCKERFILE="$SUBFOLDER/Dockerfile"

            # Check if Dockerfile exists
            if [ ! -f "$DOCKERFILE" ]; then
                echo "Skipping '$SUBFOLDER' - No Dockerfile found."
                continue
            fi

            # Get the latest tag for this subfolder image
            LATEST_TAG=$(aws ecr list-images --repository-name "$REPO_NAME" --region "$AWS_REGION" --query "imageIds[?contains(imageTag, '$IMAGE_NAME')].[imageTag]" --output text | grep -E "$IMAGE_NAME-[0-9]+" | sort -V | tail -n 1 || true)

            # Extract the last version number and increment it
            if [[ -z "$LATEST_TAG" ]]; then
                NEW_TAG="${IMAGE_NAME}-1"
            else
                LAST_NUM=$(echo "$LATEST_TAG" | sed -E "s/${IMAGE_NAME}-//")
                NEW_NUM=$((LAST_NUM + 1))
                NEW_TAG="${IMAGE_NAME}-${NEW_NUM}"
            fi

            # Define both the incremented tag and the "latest" tag
            IMAGE_URI_VERSIONED="$ECR_URL/$REPO_NAME:$NEW_TAG"
            IMAGE_URI_LATEST="$ECR_URL/$REPO_NAME:${IMAGE_NAME}-latest"

            echo "Building Docker image: $IMAGE_URI_VERSIONED"
            docker build --quiet -t "$IMAGE_URI_VERSIONED" -t "$IMAGE_URI_LATEST" -f "$DOCKERFILE" "$ROOT_PATH" || handle_error "Failed to build Docker image for '$IMAGE_NAME'"

            echo "Pushing Docker image with versioned tag to ECR..."
            docker push "$IMAGE_URI_VERSIONED" || handle_error "Failed to push versioned Docker image to ECR"

            echo "Pushing Docker image with 'latest' tag to ECR..."
            docker push "$IMAGE_URI_LATEST" || handle_error "Failed to push latest Docker image to ECR"

            echo "Successfully pushed $IMAGE_URI_VERSIONED and $IMAGE_URI_LATEST to ECR."
        fi
    done
done

echo "All Docker images have been successfully built and pushed to ECR."
