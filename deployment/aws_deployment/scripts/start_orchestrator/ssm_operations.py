#!/usr/bin/env python3
"""
SSM operations module for orchestrator deployment.
"""

import sys
import time
from typing import List

from botocore.exceptions import ClientError


def send_ssm_command(
    ssm_client, instance_id: str, commands: List[str], comment: str
) -> str:
    """
    Send commands to an EC2 instance using SSM.

    Args:
        ssm_client: Boto3 SSM client
        instance_id: EC2 instance ID
        commands: List of shell commands to execute
        comment: Comment for the SSM command

    Returns:
        Command ID of the SSM command
    """
    try:
        response = ssm_client.send_command(
            InstanceIds=[instance_id],
            DocumentName="AWS-RunShellScript",
            Parameters={"commands": commands},
            Comment=comment,
        )
        return response["Command"]["CommandId"]
    except Exception as e:
        print(f"Error sending SSM command to instance {instance_id}: {e}")
        sys.exit(1)


def wait_for_command_completion(
    ssm_client, command_id: str, instance_id: str, timeout: int = 480
) -> bool:
    """
    Wait for an SSM command to complete.

    Args:
        ssm_client: Boto3 SSM client
        command_id: Command ID to check
        instance_id: EC2 instance ID
        timeout: Maximum time to wait in seconds

    Returns:
        True if the command completed successfully, False otherwise
    """
    start_time = time.time()
    time.sleep(5)  # Initial delay to allow command to register

    while time.time() - start_time < timeout:
        try:
            response = ssm_client.get_command_invocation(
                CommandId=command_id, InstanceId=instance_id
            )

            status = response["Status"]
            if status == "Success":
                return True
            elif status in ["Failed", "Cancelled", "TimedOut"]:
                print(f"Command failed with status: {status}")
                print(
                    f"Error: {response.get('StandardErrorContent', 'No error message')}"
                )
                return False

            time.sleep(2)
        except ClientError as e:
            if "InvocationDoesNotExist" in str(e):
                print("Command invocation not found yet, waiting...")
                time.sleep(5)
                continue
            else:
                print(f"Error checking command status: {e}")
                return False

    print(f"Timed out waiting for command to complete after {timeout} seconds")
    return False


def prepare_start_commands(
    aws_region: str,
    aws_account_id: str,
    orchestrator_db_user: str,
    orchestrator_db_password: str,
    orchestrator_ip: str,
    orchestrator_type: str,
) -> List[str]:
    """
    Prepare commands to start the orchestrator server.

    Args:
        aws_region: AWS region
        aws_account_id: AWS account ID
        orchestrator_db_user: Database user
        orchestrator_db_password: Database password
        orchestrator_ip: Orchestrator IP address
        orchestrator_type: Type of orchestrator

    Returns:
        List of shell commands
    """
    return [
        "cd /opt/orchestrator",
        f"export AWS_REGION={aws_region}",
        f"export AWS_ACCOUNT_ID={aws_account_id}",
        f"export {orchestrator_type.upper()}_DB_USER={orchestrator_db_user}",
        f"export {orchestrator_type.upper()}_DB_PASSWORD={orchestrator_db_password}",
        f"export {orchestrator_type.upper()}_IP={orchestrator_ip}",
        f"export {orchestrator_type.upper()}_PORT=4200",
        f"export {orchestrator_type.upper()}_API_PORT=4201",
        "/opt/orchestrator/task_runner.sh --functions-path=/opt/orchestrator/functions.sh --detached",
    ]
