{"cSpell.words": ["amazonwebservices", "atlascode", "awscliv", "awsvpc", "<PERSON><PERSON><PERSON><PERSON>", "batele<PERSON>o", "bibha<PERSON>dn", "boto", "creds", "customresources", "datawrangler", "d<PERSON><PERSON>", "dearmor", "debugpy", "Descryption", "docsmsft", "<PERSON><PERSON><PERSON><PERSON>", "d<PERSON><PERSON>", "eamodio", "eplak", "Fargate", "firecrawl", "Firecrawl", "g<PERSON><PERSON>", "gruntfuggly", "haberdashpi", "hbenl", "hentzd", "Imdsv", "ionutvmi", "isort", "jkju<PERSON><PERSON><PERSON>", "kevin<PERSON>", "keyrings", "kohler", "LEBRON", "littlefoxteam", "m<PERSON>tchie", "moby", "nachocab", "namify", "njpwerner", "nodesource", "nodistro", "noninteractive", "NOPASSWD", "Opensearch", "opensearchservice", "pkief", "polacode", "pycache", "pylance", "pylint", "PYTHONPATH", "RAPM", "rickard", "rioj", "r<PERSON><PERSON><PERSON>", "rubymaniac", "shakram", "shellcheck", "stackbreak", "stepfunctions", "subword", "tacticaldan", "timo<PERSON><PERSON><PERSON>", "toolsai", "uppercasesql", "venv", "visualstudioexptteam", "VORP", "vscodeintellicode", "wordgenera", "yzhang", "ziya<PERSON>"]}