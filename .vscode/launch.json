{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2"
            }
        },
        {
            "name": "CDK App Testing",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/app.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2"
            }
            // "args": [
            // "${command:pickArgs}"
            // ]
        },
        {
            "name": "Start Prefect",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/scripts/copy_and_start_prefect.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
            }
        },
        {
            "name": "Copy and Start Orchestrator",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/scripts/start_orchestrator/start_orchestrator.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
            }
        },
        {
            "name": "Update Secrets",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/deployment/aws_deployment/deployment_helpers/update_secrets.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
                "API_KEY_BALLDONTLIE": "abc"
            }
        },
        {
            "name": "Extraction",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/src/tasks/extraction/extraction.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
                "ENDPOINTS": "teams",
                "PREFECT_API_URL": "http://host.docker.internal:4200/api"
            }
        },
        {
            "name": "Prefect Bootstrap",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/docker/prefect/bootstrap_deployment_context.py",
            "console": "integratedTerminal",
            "env": {
                "AWS_CONFIG_PATH": "${workspaceFolder}/shared/config/aws_config.yaml",
                "AWS_REGION": "us-east-2",
                "PREFECT_API_URL": "http://host.docker.internal:4200/api"
            }
        }
    ]
}