#!/bin/bash
# .devcontainer/scripts/post_create_command.sh

# Get the directory of the current script
SCRIPT_DIR="$(dirname "$0")"

# Define the script to run
DEPENDENCIES_SCRIPT="$SCRIPT_DIR/python_env_dependencies.sh"

# Check if the script exists
if [[ -f "$DEPENDENCIES_SCRIPT" ]]; then
    echo "Running $DEPENDENCIES_SCRIPT..."
    bash "$DEPENDENCIES_SCRIPT"
else
    echo "Error: $DEPENDENCIES_SCRIPT not found!"
    exit 1
fi

# Export AWS secrets as environment variables
EXPORT_SECRETS_SCRIPT="$SCRIPT_DIR/export_aws_secrets.py"
if [[ -f "$EXPORT_SECRETS_SCRIPT" ]]; then
    echo "Exporting AWS secrets as environment variables..."
    python "$EXPORT_SECRETS_SCRIPT"
else
    echo "Warning: $EXPORT_SECRETS_SCRIPT not found. Skipping secret export."
fi

# eval "$(ssh-agent -s)"
# ssh-add /home/<USER>/.ssh/github_personal

cp /workspace/package-lock.json /betting_exchange
cp /workspace/package.json /betting_exchange