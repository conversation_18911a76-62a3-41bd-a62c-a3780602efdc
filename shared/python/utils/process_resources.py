def get_process_resources(aws_config, process_name):
    """
    Returns all infrastructure resources that have the specified process_name as a consumer.

    Args:
        aws_config (dict): The AWS configuration dictionary
        process_name (str): The name of the process to filter resources by

    Returns:
        dict: A dictionary containing all resources (buckets, secrets, etc.) that have
              the specified process_name as a consumer across all stacks
    """
    process_resources = {}

    # Get stacks from infrastructure
    if "infrastructure" in aws_config and "stacks" in aws_config["infrastructure"]:
        stacks = aws_config["infrastructure"]["stacks"]

        # Iterate through each stack
        for stack_name, stack_config in stacks.items():
            # Check all resource types in the stack
            for resource_type, resources in stack_config.items():
                # Skip non-resource properties (like 'id')
                if not isinstance(resources, list):
                    continue

                # Process each resource of the current type
                for resource in resources:
                    # Check if resource has consumer_processes and if process_name is in it
                    if (
                        "consumer-processes" in resource
                        and process_name in resource["consumer-processes"]
                    ):
                        # Initialize resource type in result dict if not exists
                        if resource_type not in process_resources:
                            process_resources[resource_type] = []

                        # Add matching resource to the result
                        process_resources[resource_type].append(resource.get("id"))

    return process_resources
