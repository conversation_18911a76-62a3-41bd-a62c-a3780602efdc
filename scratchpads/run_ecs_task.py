import json
import sys

import boto3

from shared.python.utils.get_aws_config import get_aws_config


def run_ecs_task(
    cluster_name,
    task_definition,
    subnet_ids=None,
    security_groups=None,
    aws_region=None,
):
    """
    Kicks off an ECS task using the specified task definition and cluster

    Parameters:
    - cluster_name: Name of the ECS cluster
    - task_definition: Name or ARN of the task definition
    - subnet_ids: List of subnet IDs for the task (optional)
    - security_groups: List of security group IDs (optional)
    - aws_region: AWS region to connect to (optional)

    Returns:
    - Response from the run_task API call
    """
    # Initialize ECS client
    if aws_region:
        ecs_client = boto3.client("ecs", region_name=aws_region)
    else:
        ecs_client = boto3.client("ecs")

    # Define task parameters
    run_task_params = {
        "cluster": cluster_name,
        "taskDefinition": task_definition,
        "launchType": "FARGATE",  # Assuming Fargate, change if using EC2
    }

    # Add network configuration if subnet IDs are provided
    if subnet_ids:
        network_config = {
            "awsvpcConfiguration": {
                "subnets": subnet_ids,
                "assignPublicIp": "ENABLED",  # Change to DISABLED if not needed
            }
        }

        if security_groups:
            network_config["awsvpcConfiguration"]["securityGroups"] = security_groups

        run_task_params["networkConfiguration"] = network_config

    try:
        # Run the task
        response = ecs_client.run_task(**run_task_params)
        return response
    except Exception as e:
        print(f"Error running ECS task: {e}")
        return None


def get_parameter_store_resources(config_stack):
    """
    Recursively extracts IDs from resources that have create-parameter-store-variable-with-arn set to true

    Parameters:
    - config_stack: Dictionary containing configuration for AWS resources

    Returns:
    - List of dictionaries containing resource 'id' and 'path' where create-parameter-store-variable-with-arn is true
    """
    resources = []

    def traverse_dict(d, path=""):
        if isinstance(d, dict):
            # Check if this dictionary has both 'id' and 'create-parameter-store-variable-with-arn' = True
            if "id" in d and d.get("create-parameter-store-variable-with-arn") is True:
                resources.append({"id": d["id"], "path": path})

            # Continue recursively traversing the dictionary
            for key, value in d.items():
                new_path = f"{path}.{key}" if path else key
                traverse_dict(value, new_path)
        elif isinstance(d, list):
            # Traverse each item in the list
            for i, item in enumerate(d):
                new_path = f"{path}[{i}]"
                traverse_dict(item, new_path)

    traverse_dict(config_stack)
    return resources


def find_matching_items_in_list(list, key):
    return next(
        (item for item in list if key in item),
        None,
    )


def get_param_value(ssm_client, param_name):
    # Get the parameter value from Parameter Store
    try:
        param = ssm_client.get_parameter(Name=param_name, WithDecryption=True)
        return param["Parameter"]["Value"]
    except Exception as e:
        print(f"Error retrieving parameter {param_name} from Parameter Store: {e}")
        sys.exit(1)


def get_param_values_from_stack_config(config, keys):
    """
    Extracts parameter values from the stack configuration based on the provided keys.

    Parameters:
    - config: Dictionary containing stack configuration
    - keys: List of keys to extract values for
    """
    # Get resources with parameter store variables
    param_resources_config = get_parameter_store_resources(config)
    param_resources = [resource.get("id") for resource in param_resources_config]

    # Initialize SSM client
    ssm_client = boto3.client("ssm")
    return_tuples = []
    for key in keys:
        param_name = find_matching_items_in_list(param_resources, key)
        param_value = get_param_value(ssm_client, param_name)
        return_tuples.append((param_value))

    return return_tuples


def get_subnets_by_name_keyword(vpc_id, keyword, aws_region=None):
    """
    Returns subnet IDs for subnets in the specified VPC that have the keyword in their Name tag

    Parameters:
    - vpc_id: ID of the VPC to search in
    - keyword: Keyword to search for in subnet names
    - aws_region: AWS region to connect to (optional)

    Returns:
    - List of matching subnet IDs
    """
    # Initialize EC2 client
    if aws_region:
        ec2_client = boto3.client("ec2", region_name=aws_region)
    else:
        ec2_client = boto3.client("ec2")

    # Get all subnets in the specified VPC
    response = ec2_client.describe_subnets(
        Filters=[{"Name": "vpc-id", "Values": [vpc_id]}]
    )

    matching_subnet_ids = []

    # Filter subnets by Name tag containing the keyword
    for subnet in response["Subnets"]:
        subnet_id = subnet["SubnetId"]
        # Check if the subnet has tags
        if "Tags" in subnet:
            # Look for the Name tag
            for tag in subnet["Tags"]:
                if tag["Key"] == "Name" and keyword.lower() in tag["Value"].lower():
                    matching_subnet_ids.append(subnet_id)
                    break

    return matching_subnet_ids


if __name__ == "__main__":
    aws_config = get_aws_config()
    aws_region = aws_config.get("aws-region")

    extraction_config = (
        aws_config.get("infrastructure", {}).get("stacks", {}).get("extraction", {})
    )

    cluster, task = get_param_values_from_stack_config(
        extraction_config, ["cluster", "tasks-api"]
    )

    network_config = (
        aws_config.get("infrastructure", {}).get("stacks", {}).get("network", {})
    )

    vpc, security_groups = get_param_values_from_stack_config(
        network_config, ["vpc", "ecs-sg"]
    )

    # Get private subnets for Fargate tasks
    subnet_ids = get_subnets_by_name_keyword(vpc, "private")

    if not subnet_ids:
        print("No matching subnets found. Using default subnet IDs.")
        exit(1)

    print(f"Starting ECS task with definition: {task}")
    print(f"On cluster: {cluster}")

    response = run_ecs_task(
        cluster_name=cluster,
        task_definition=task,
        subnet_ids=["subnet-0ac841b68019ea901"],
        # subnet_ids=subnet_ids,
        security_groups=[security_groups],
        aws_region=aws_region,
    )

    if response:
        print("Task started successfully:")
        print(json.dumps(response, indent=2, default=str))

        # Extract and display task ARN
        if "tasks" in response and response["tasks"]:
            task_arn = response["tasks"][0]["taskArn"]
            print(f"\nTask ARN: {task_arn}")
        else:
            print("\nNo tasks were started.")
    else:
        print("Failed to start task.")
        sys.exit(1)
